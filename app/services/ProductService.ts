import db from '../db.server';
import { GET_SHOP_INFO } from './queries/shopQuery';
import type { AdminApiContext } from '@shopify/shopify-app-remix/server';
import { GET_PRODUCTS_WITH_CURSOR } from './queries/productQuery';

export class ProductService {
  public static async getProducts(session, shop) {
    console.log(db);
    console.log(session);
    return false;
  }

  public static async upsertShop(admin: AdminApiContext) {
    const response = await admin.graphql(GET_SHOP_INFO);

    if (response.status === 200) {
      const { data: {shop} } = await response.json();

      if (!shop) {
        return false;
      }

      try {
        const existingShop = await db.shop.findFirst({
          where: {
            shopifyId: shop.id,
          }
        });


        if (existingShop) {
          return existingShop;
        }

        const newShop = await db.shop.create({
          data: {
            shopifyId: shop.id,
            name: shop.name,
            myshopifyDomain: shop.myshopifyDomain,
            email: shop.email,
            url: shop.url,
          },
        });

        return newShop;

      } catch (e) {
        console.error('Error inserting shop', e);
      }
    }

    return false;
  }

  public static async syncProducts(admin: AdminApiContext) {
    const shop = await this.upsertShop(admin);
    if(!shop.isProductSynced) {
      let cursor = null;
      let hasNextPage = true;

      try {
        while (hasNextPage) {
          const response = await admin.graphql(GET_PRODUCTS_WITH_CURSOR, {
            variables: {
              first: 10,
              cursor: cursor,
            }
          });

          const { data: {products: {pageInfo, nodes}} } = await response.json();

          const productInsertRequest = nodes.map(async (product) => {
            return this.insertProduct(product, shop.id);
          });

          const syncedProducts = await Promise.all(productInsertRequest);

          console.log(`Products synced count - ${syncedProducts.length}`);

          hasNextPage = pageInfo.hasNextPage;
          cursor = pageInfo.endCursor;
        }
      } catch (e) {
        console.error('Error syncing products', e);
      }
    }
  }


  public static async insertProduct(product, shopId) {
    const { id, title, handle, productType, status, description, media } = product;

    try {
      const existingProduct = await db.product.findFirst({
        where: {
          shopifyId: id,
        }
      });

      if (existingProduct) {
        return existingProduct;
      }

      const imageUrl = media?.nodes[0]?.image?.url || null;

      return  db.product.create({
        data: {
          shopifyId: id,
          title,
          description,
          handle,
          status,
          productType,
          shopId,
          imageUrl
        },
      });

    } catch (e) {
      console.error('Error inserting product', e);
    }

    return false;
  }


}
